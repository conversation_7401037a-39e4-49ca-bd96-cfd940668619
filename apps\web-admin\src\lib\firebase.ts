/**
 * Firebase Configuration for Admin App
 * Uses shared @encreasl/auth package for enterprise-level authentication
 */

import {
  initializeAdminFirebase,
  createFirebaseConfigFromEnv,
  getFirebaseAuth,
  getCurrentFirebaseApp
} from '@encreasl/auth';
import { getFirestore, Firestore } from 'firebase/firestore';

// Initialize Firebase for admin app using shared configuration
let app: ReturnType<typeof getCurrentFirebaseApp> | null = null;
let db: Firestore | null = null;

/**
 * Initialize Firebase services for admin app
 * Uses the shared auth package for consistent configuration
 */
export function initializeFirebase() {
  try {
    // Initialize admin Firebase app using shared package
    const firebaseConfig = createFirebaseConfigFromEnv(true);
    app = initializeAdminFirebase(firebaseConfig);

    // Initialize Firestore
    if (!db && app) {
      db = getFirestore(app);
    }

    return {
      app,
      auth: getFirebaseAuth(),
      db,
    };
  } catch (error) {
    console.error('Failed to initialize Firebase for admin app:', error);
    throw error;
  }
}

// Initialize Firebase services
const firebase = initializeFirebase();

export const firebaseApp = firebase.app;
export const firebaseAuth = firebase.auth;
export const firebaseDb = firebase.db;

// Export Firebase config for reference
export const firebaseConfig = createFirebaseConfigFromEnv(true);

// Helper function to check if Firebase is initialized
export function isFirebaseInitialized(): boolean {
  return !!getCurrentFirebaseApp();
}

// Helper function to get Firebase app instance
export function getFirebaseApp() {
  const app = getCurrentFirebaseApp();
  if (!app) {
    throw new Error('Firebase is not initialized. Call initializeFirebase() first.');
  }
  return app;
}
