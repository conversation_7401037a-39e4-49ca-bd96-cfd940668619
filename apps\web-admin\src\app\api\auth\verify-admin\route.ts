import { NextRequest, NextResponse } from 'next/server';
import { getAdminAuth, verifyIdToken } from '@/lib/firebase-admin';

// POST /api/auth/verify-admin
export async function POST(request: NextRequest) {
  try {
    const { idToken } = await request.json();

    if (!idToken) {
      return NextResponse.json(
        { error: 'ID token is required' },
        { status: 400 }
      );
    }

    // Verify the ID token
    const decodedToken = await verifyIdToken(idToken);
    
    // Check if user exists and has admin privileges
    const auth = getAdminAuth();
    const user = await auth.getUser(decodedToken.uid);

    // For the specific admin user, set custom claims if not already set
    if (user.email === '<EMAIL>' && !user.customClaims?.isAdmin) {
      await auth.setCustomUserClaims(user.uid, {
        isAdmin: true,
        isSuperAdmin: true,
        permissions: ['*'], // All permissions
        role: 'super-admin'
      });
    }

    // Verify user has admin claims
    const updatedUser = await auth.getUser(decodedToken.uid);
    if (!updatedUser.customClaims?.isAdmin) {
      return NextResponse.json(
        { error: 'Access denied. Admin privileges required.' },
        { status: 403 }
      );
    }

    return NextResponse.json({
      success: true,
      user: {
        uid: updatedUser.uid,
        email: updatedUser.email,
        emailVerified: updatedUser.emailVerified,
        customClaims: updatedUser.customClaims,
      }
    });

  } catch (error) {
    console.error('Admin verification error:', error);
    return NextResponse.json(
      { error: 'Failed to verify admin user' },
      { status: 500 }
    );
  }
}
