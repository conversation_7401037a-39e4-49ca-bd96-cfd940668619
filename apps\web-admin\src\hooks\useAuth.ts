'use client';

import {
  useAdminAuth,
  useAdminAuthState,
  type AdminUserProfile,
  type EmailPasswordCredentials
} from '@encreasl/auth';
import { useAuth as useAuthContext, useAuthState, useAdminUser } from '@/contexts/AuthContext';

// Re-export hooks from context for convenience
export const useAuth = useAuthContext;
export { useAuthState, useAdminUser };

// Additional custom hooks for specific use cases

// Hook for protected routes
export function useRequireAuth() {
  const { isAuthenticated, loading } = useAdminAuthState();
  const { profile } = useAdminAuth();

  return {
    user: profile,
    loading,
    isAuthenticated,
    requiresAuth: !loading && !isAuthenticated,
  };
}

// Hook for admin operations
export function useAdminOperations() {
  const { profile, signOut, hasPermission, isSuperAdmin, isAdmin } = useAdminAuth();

  return {
    user: profile,
    signOut,
    hasPermission,
    isSuperAdmin,
    isAdmin,
  };
}

// Hook for form authentication
export function useAuthForm() {
  const { signInAdmin, loading, error, clearError } = useAuth();

  // Wrapper function to match the expected interface
  const signIn = async (credentials: EmailPasswordCredentials) => {
    return await signInAdmin(credentials);
  };

  return {
    signIn,
    loading,
    error: error?.message || null,
    clearError,
    isSubmitting: loading,
  };
}
