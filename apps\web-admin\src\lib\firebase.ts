import { initializeApp, getApps, FirebaseApp } from 'firebase/app';
import { getAuth, Auth } from 'firebase/auth';
import { getFirestore, Firestore } from 'firebase/firestore';

// Firebase configuration
const firebaseConfig = {
  apiKey: process.env.NEXT_PUBLIC_FIREBASE_API_KEY,
  authDomain: process.env.NEXT_PUBLIC_FIREBASE_AUTH_DOMAIN,
  projectId: process.env.NEXT_PUBLIC_FIREBASE_PROJECT_ID,
  storageBucket: process.env.NEXT_PUBLIC_FIREBASE_STORAGE_BUCKET,
  messagingSenderId: process.env.NEXT_PUBLIC_FIREBASE_MESSAGING_SENDER_ID,
  appId: process.env.NEXT_PUBLIC_FIREBASE_APP_ID,
};

// Validate Firebase configuration
function validateFirebaseConfig() {
  const requiredFields = [
    'apiKey',
    'authDomain', 
    'projectId',
    'storageBucket',
    'messagingSenderId',
    'appId'
  ];

  for (const field of requiredFields) {
    if (!firebaseConfig[field as keyof typeof firebaseConfig]) {
      throw new Error(`Firebase configuration missing: ${field}`);
    }
  }
}

// Initialize Firebase App (singleton pattern)
let app: FirebaseApp;
let auth: Auth;
let db: Firestore;

// Initialize Firebase app
function initializeFirebaseApp(): FirebaseApp {
  if (getApps().length === 0) {
    validateFirebaseConfig();
    app = initializeApp(firebaseConfig);
  } else {
    app = getApps()[0];
  }
  return app;
}

// Initialize Firebase services
export function initializeFirebase() {
  if (!app) {
    app = initializeFirebaseApp();
  }

  // Initialize Auth
  if (!auth) {
    auth = getAuth(app);
  }

  // Initialize Firestore
  if (!db) {
    db = getFirestore(app);
  }

  return { app, auth, db };
}

// Export initialized services
const firebase = initializeFirebase();

export const firebaseApp = firebase.app;
export const firebaseAuth = firebase.auth;
export const firebaseDb = firebase.db;

// Export Firebase config for reference
export { firebaseConfig };

// Helper function to check if Firebase is initialized
export function isFirebaseInitialized(): boolean {
  return getApps().length > 0;
}

// Helper function to get Firebase app instance
export function getFirebaseApp(): FirebaseApp {
  if (!isFirebaseInitialized()) {
    throw new Error('Firebase is not initialized. Call initializeFirebase() first.');
  }
  return getApps()[0];
}
