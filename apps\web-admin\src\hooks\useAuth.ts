'use client';

import { useAuth as useAuthContext, useAuthState, useAdminUser } from '@/contexts/AuthContext';

// Re-export hooks from context for convenience
export const useAuth = useAuthContext;
export { useAuthState, useAdminUser };

// Additional custom hooks for specific use cases

// Hook for protected routes
export function useRequireAuth() {
  const { user, loading } = useAuthState();
  
  return {
    user,
    loading,
    isAuthenticated: !!user,
    requiresAuth: !loading && !user,
  };
}

// Hook for admin operations
export function useAdminOperations() {
  const { user, signOut } = useAuth();
  
  const hasPermission = (permission: string): boolean => {
    if (!user?.customClaims) return false;
    
    // Check if user has specific permission
    const permissions = user.customClaims.permissions as string[] || [];
    return permissions.includes(permission) || user.customClaims.isSuperAdmin;
  };
  
  const isSuperAdmin = (): boolean => {
    return !!user?.customClaims?.isSuperAdmin;
  };
  
  return {
    user,
    signOut,
    hasPermission,
    isSuperAdmin,
    isAdmin: !!user?.isAdmin,
  };
}

// Hook for form authentication
export function useAuthForm() {
  const { signIn, loading, error, clearError } = useAuth();
  
  return {
    signIn,
    loading,
    error,
    clearError,
    isSubmitting: loading,
  };
}
