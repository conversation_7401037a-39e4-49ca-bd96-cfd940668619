'use client';

import React, { createContext, useContext, useEffect, useState, ReactNode } from 'react';
import { initializeAdminFirebase, createFirebaseConfigFromEnv } from '@encreasl/auth';

interface FirebaseContextType {
  initialized: boolean;
  error: string | null;
}

const FirebaseContext = createContext<FirebaseContextType | undefined>(undefined);

interface FirebaseProviderProps {
  children: ReactNode;
}

/**
 * Firebase Provider Component
 * Ensures Firebase is properly initialized before rendering children
 */
export function FirebaseProvider({ children }: FirebaseProviderProps) {
  const [initialized, setInitialized] = useState(false);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const initializeFirebase = async () => {
      try {
        // Create Firebase configuration from environment variables
        const config = createFirebaseConfigFromEnv(true);
        
        // Initialize Firebase for admin app
        await initializeAdminFirebase(config);
        
        setInitialized(true);
        console.log('🔥 Firebase initialized successfully');
      } catch (err) {
        const errorMessage = err instanceof Error ? err.message : 'Failed to initialize Firebase';
        setError(errorMessage);
        console.error('❌ Firebase initialization failed:', err);
      }
    };

    initializeFirebase();
  }, []);

  // Show loading state while Firebase is initializing
  if (!initialized && !error) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Initializing Firebase...</p>
        </div>
      </div>
    );
  }

  // Show error state if Firebase initialization failed
  if (error) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50">
        <div className="text-center max-w-md mx-auto p-6">
          <div className="text-red-600 text-6xl mb-4">⚠️</div>
          <h1 className="text-2xl font-bold text-gray-900 mb-2">Firebase Initialization Failed</h1>
          <p className="text-gray-600 mb-4">{error}</p>
          <button 
            onClick={() => window.location.reload()} 
            className="bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700"
          >
            Retry
          </button>
        </div>
      </div>
    );
  }

  // Firebase is initialized, render children
  return (
    <FirebaseContext.Provider value={{ initialized, error }}>
      {children}
    </FirebaseContext.Provider>
  );
}

/**
 * Hook to access Firebase initialization status
 */
export function useFirebase(): FirebaseContextType {
  const context = useContext(FirebaseContext);
  if (context === undefined) {
    throw new Error('useFirebase must be used within a FirebaseProvider');
  }
  return context;
}

/**
 * Higher-order component to ensure Firebase is initialized
 */
export function withFirebase<P extends object>(Component: React.ComponentType<P>) {
  return function FirebaseWrappedComponent(props: P) {
    return (
      <FirebaseProvider>
        <Component {...props} />
      </FirebaseProvider>
    );
  };
}
