'use client';

import React, { createContext, useContext, ReactNode } from 'react';
import {
  useAdminAuth,
  useAuth,
  type AdminUserProfile,
  type EmailPasswordCredentials,
  type UseAdminAuthReturn
} from '@encreasl/auth';

// Auth context type - extends the shared admin auth functionality
interface AuthContextType extends UseAdminAuthReturn {
  clearError: () => void;
}

// Create context
const AuthContext = createContext<AuthContextType | undefined>(undefined);

// Auth provider props
interface AuthProviderProps {
  children: ReactNode;
}

// Auth provider component
export function AuthProvider({ children }: AuthProviderProps) {
  // Use the shared admin auth hook
  const adminAuth = useAdminAuth();
  const baseAuth = useAuth();

  // Clear error function (extends the shared functionality)
  const clearError = () => {
    // The shared auth package handles error clearing internally
    // This is here for backward compatibility
  };

  const value: AuthContextType = {
    ...adminAuth,
    clearError,
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
}

// Custom hook to use auth context
export function useAuth(): AuthContextType {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
}

// Custom hook for auth state only
export function useAuthState() {
  const { user, loading, isAdmin } = useAuth();
  return {
    user: user,
    loading,
    isAuthenticated: !!user,
    isAdmin,
  };
}

// Custom hook for admin user
export function useAdminUser() {
  const { profile } = useAuth();
  return profile;
}
