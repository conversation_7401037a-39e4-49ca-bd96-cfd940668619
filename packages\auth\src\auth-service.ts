/**
 * Authentication Service
 * Based on Firebase Auth v10 official documentation
 * https://firebase.google.com/docs/auth/web/start
 */

import {
  signInWithEmailAndPassword as firebaseSignInWithEmailAndPassword,
  createUserWithEmailAndPassword,
  signOut as firebaseSignOut,
  sendPasswordResetEmail as firebaseSendPasswordResetEmail,
  sendEmailVerification as firebaseSendEmailVerification,
  updateProfile as firebaseUpdateProfile,
  GoogleAuthProvider,
  signInWithPopup,
  onAuthStateChanged,
  User as FirebaseUser,
  UserCredential,
  Unsubscribe,
  AuthError as FirebaseAuthError,
} from 'firebase/auth';

import { getFirebaseAuth } from './firebase-config';
import {
  AuthError,
  EmailPasswordCredentials,
  SignUpData,
  AuthEventType,
  AdminUserProfile
} from './types';

// ========================================
// AUTHENTICATION SERVICE CLASS
// ========================================

export class AuthService {
  private auth = getFirebaseAuth();
  private eventListeners: ((event: AuthEventType, user: FirebaseUser | null) => void)[] = [];

  // ========================================
  // CORE AUTHENTICATION METHODS
  // ========================================

  /**
   * Sign in with email and password
   * https://firebase.google.com/docs/auth/web/password-auth#sign_in_a_user_with_an_email_address_and_password
   */
  async signInWithEmailAndPassword(credentials: EmailPasswordCredentials): Promise<UserCredential> {
    try {
      const userCredential = await firebaseSignInWithEmailAndPassword(
        this.auth,
        credentials.email,
        credentials.password
      );
      
      this.emitEvent('sign-in', userCredential.user);
      return userCredential;
    } catch (error) {
      throw this.handleAuthError(error as FirebaseAuthError);
    }
  }

  /**
   * Create new user with email and password
   * https://firebase.google.com/docs/auth/web/password-auth#create_a_password-based_account
   */
  async signUpWithEmailAndPassword(data: SignUpData): Promise<UserCredential> {
    try {
      const userCredential = await createUserWithEmailAndPassword(
        this.auth,
        data.email,
        data.password
      );

      // Update profile with display name if provided
      if (data.displayName && userCredential.user) {
        await firebaseUpdateProfile(userCredential.user, {
          displayName: data.displayName
        });
      }

      // Send email verification
      if (userCredential.user) {
        await firebaseSendEmailVerification(userCredential.user);
      }

      this.emitEvent('sign-up', userCredential.user);
      return userCredential;
    } catch (error) {
      throw this.handleAuthError(error as FirebaseAuthError);
    }
  }

  /**
   * Sign in with Google
   * https://firebase.google.com/docs/auth/web/google-signin
   */
  async signInWithGoogle(): Promise<UserCredential> {
    try {
      const provider = new GoogleAuthProvider();
      provider.addScope('email');
      provider.addScope('profile');

      const userCredential = await signInWithPopup(this.auth, provider);
      this.emitEvent('sign-in', userCredential.user);
      return userCredential;
    } catch (error) {
      throw this.handleAuthError(error as FirebaseAuthError);
    }
  }

  /**
   * Sign out current user
   * https://firebase.google.com/docs/auth/web/password-auth#sign_out_a_user
   */
  async signOut(): Promise<void> {
    try {
      await firebaseSignOut(this.auth);
      this.emitEvent('sign-out', null);
    } catch (error) {
      throw this.handleAuthError(error as FirebaseAuthError);
    }
  }

  /**
   * Send password reset email
   * https://firebase.google.com/docs/auth/web/manage-users#send_a_password_reset_email
   */
  async sendPasswordResetEmail(email: string): Promise<void> {
    try {
      await firebaseSendPasswordResetEmail(this.auth, email);
      this.emitEvent('password-reset', null);
    } catch (error) {
      throw this.handleAuthError(error as FirebaseAuthError);
    }
  }

  /**
   * Send email verification
   * https://firebase.google.com/docs/auth/web/manage-users#send_a_user_a_verification_email
   */
  async sendEmailVerification(): Promise<void> {
    const user = this.getCurrentUser();
    if (!user) {
      throw new Error('No user is currently signed in');
    }

    try {
      await firebaseSendEmailVerification(user);
      this.emitEvent('email-verification', user);
    } catch (error) {
      throw this.handleAuthError(error as FirebaseAuthError);
    }
  }

  /**
   * Update user profile
   * https://firebase.google.com/docs/auth/web/manage-users#update_a_users_profile
   */
  async updateProfile(data: { displayName?: string; photoURL?: string }): Promise<void> {
    const user = this.getCurrentUser();
    if (!user) {
      throw new Error('No user is currently signed in');
    }

    try {
      await firebaseUpdateProfile(user, data);
      this.emitEvent('profile-update', user);
    } catch (error) {
      throw this.handleAuthError(error as FirebaseAuthError);
    }
  }

  // ========================================
  // USER STATE METHODS
  // ========================================

  /**
   * Get current user
   * https://firebase.google.com/docs/auth/web/manage-users#get_the_currently_signed-in_user
   */
  getCurrentUser(): FirebaseUser | null {
    return this.auth.currentUser;
  }

  /**
   * Listen to authentication state changes
   * https://firebase.google.com/docs/auth/web/manage-users#get_the_currently_signed-in_user
   */
  onAuthStateChanged(callback: (user: FirebaseUser | null) => void): Unsubscribe {
    return onAuthStateChanged(this.auth, callback);
  }

  /**
   * Get ID token for API calls
   * https://firebase.google.com/docs/auth/web/manage-users#get_a_users_profile
   */
  async getIdToken(forceRefresh = false): Promise<string | null> {
    const user = this.getCurrentUser();
    if (!user) return null;

    try {
      return await user.getIdToken(forceRefresh);
    } catch (error) {
      console.error('Error getting ID token:', error);
      return null;
    }
  }

  // ========================================
  // UTILITY METHODS
  // ========================================

  /**
   * Check if user is authenticated
   */
  isAuthenticated(): boolean {
    return this.getCurrentUser() !== null;
  }

  /**
   * Check if user email is verified
   */
  isEmailVerified(): boolean {
    const user = this.getCurrentUser();
    return user ? user.emailVerified : false;
  }

  // ========================================
  // EVENT HANDLING
  // ========================================

  private emitEvent(type: AuthEventType, user: FirebaseUser | null): void {
    this.eventListeners.forEach(listener => {
      try {
        listener(type, user);
      } catch (error) {
        console.error('Error in auth event listener:', error);
      }
    });
  }

  addEventListener(listener: (event: AuthEventType, user: FirebaseUser | null) => void): void {
    this.eventListeners.push(listener);
  }

  removeEventListener(listener: (event: AuthEventType, user: FirebaseUser | null) => void): void {
    const index = this.eventListeners.indexOf(listener);
    if (index > -1) {
      this.eventListeners.splice(index, 1);
    }
  }

  // ========================================
  // ERROR HANDLING
  // ========================================

  private handleAuthError(error: FirebaseAuthError): AuthError {
    return {
      code: error.code,
      message: this.getAuthErrorMessage(error.code),
    };
  }

  private getAuthErrorMessage(code: string): string {
    switch (code) {
      case 'auth/user-not-found':
        return 'No account found with this email address.';
      case 'auth/wrong-password':
        return 'Incorrect password.';
      case 'auth/email-already-in-use':
        return 'An account with this email already exists.';
      case 'auth/weak-password':
        return 'Password should be at least 6 characters.';
      case 'auth/invalid-email':
        return 'Invalid email address.';
      case 'auth/user-disabled':
        return 'This account has been disabled.';
      case 'auth/too-many-requests':
        return 'Too many failed attempts. Please try again later.';
      case 'auth/network-request-failed':
        return 'Network error. Please check your connection.';
      case 'auth/popup-closed-by-user':
        return 'Sign-in popup was closed before completion.';
      case 'auth/cancelled-popup-request':
        return 'Sign-in was cancelled.';
      case 'auth/api-key-not-valid':
        return 'Invalid API key. Please check your Firebase configuration.';
      default:
        return 'An error occurred during authentication.';
    }
  }

  // ========================================
  // ADMIN-SPECIFIC METHODS
  // ========================================

  /**
   * Sign in admin user with additional verification
   */
  async signInAdmin(credentials: EmailPasswordCredentials): Promise<UserCredential> {
    try {
      const userCredential = await this.signInWithEmailAndPassword(credentials);

      // Get ID token with claims to verify admin status
      const idTokenResult = await userCredential.user.getIdTokenResult(true);
      const customClaims = idTokenResult.claims;

      // Verify user has admin privileges
      if (!customClaims.isAdmin) {
        await this.signOut();
        throw new Error('Access denied. Admin privileges required.');
      }

      // Verify email is verified for admin users
      if (!userCredential.user.emailVerified) {
        await this.signOut();
        throw new Error('Email verification required for admin access.');
      }

      this.emitEvent('sign-in', userCredential.user);
      return userCredential;
    } catch (error) {
      if (error instanceof Error) {
        throw error;
      }
      throw this.handleAuthError(error as FirebaseAuthError);
    }
  }

  /**
   * Get admin profile from Firebase custom claims
   */
  async getAdminProfile(user: FirebaseUser): Promise<AdminUserProfile | null> {
    try {
      const idTokenResult = await user.getIdTokenResult(true);
      const customClaims = idTokenResult.claims;

      if (!customClaims.isAdmin) {
        return null;
      }

      return {
        uid: user.uid,
        email: user.email,
        displayName: user.displayName,
        photoURL: user.photoURL,
        emailVerified: user.emailVerified,
        createdAt: new Date(user.metadata.creationTime!),
        lastLoginAt: user.metadata.lastSignInTime ? new Date(user.metadata.lastSignInTime) : null,
        isAdmin: Boolean(customClaims.isAdmin),
        isSuperAdmin: Boolean(customClaims.isSuperAdmin),
        role: (customClaims.role as string) || 'admin',
        permissions: (customClaims.permissions as string[]) || [],
        department: customClaims.department as string,
        title: customClaims.title as string,
        mfaEnabled: Boolean(customClaims.mfaEnabled),
        sessionTimeout: (customClaims.sessionTimeout as number) || 3600,
        customClaims,
        lastActivity: new Date(),
        accessLevel: (customClaims.accessLevel as number) || 1,
      };
    } catch (error) {
      console.error('Error getting admin profile:', error);
      return null;
    }
  }

  /**
   * Verify admin session is still valid
   */
  async verifyAdminSession(user: FirebaseUser): Promise<boolean> {
    try {
      const idTokenResult = await user.getIdTokenResult(true);
      const customClaims = idTokenResult.claims;

      return Boolean(customClaims.isAdmin) && user.emailVerified;
    } catch (error) {
      console.error('Error verifying admin session:', error);
      return false;
    }
  }

  /**
   * Check if user has specific admin permission
   */
  hasAdminPermission(profile: AdminUserProfile | null, permission: string): boolean {
    if (!profile) return false;
    if (profile.isSuperAdmin) return true;
    return profile.permissions.includes(permission) || profile.permissions.includes('*');
  }
}

// ========================================
// SINGLETON INSTANCE
// ========================================

let authServiceInstance: AuthService | null = null;

export function getAuthService(): AuthService {
  if (!authServiceInstance) {
    authServiceInstance = new AuthService();
  }
  return authServiceInstance;
}

// ========================================
// VALIDATION UTILITIES
// ========================================

export function validateEmail(email: string): boolean {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
}

export function validatePassword(password: string): { valid: boolean; errors: string[] } {
  const errors: string[] = [];

  if (password.length < 6) {
    errors.push('Password must be at least 6 characters long');
  }

  if (!/[A-Z]/.test(password)) {
    errors.push('Password must contain at least one uppercase letter');
  }

  if (!/[a-z]/.test(password)) {
    errors.push('Password must contain at least one lowercase letter');
  }

  if (!/\d/.test(password)) {
    errors.push('Password must contain at least one number');
  }

  return {
    valid: errors.length === 0,
    errors
  };
}
