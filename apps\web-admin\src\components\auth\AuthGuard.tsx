'use client';

import { useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { Shield, Loader2 } from 'lucide-react';
import { useRequireAuth } from '@/hooks/useAuth';

interface AuthGuardProps {
  children: React.ReactNode;
  fallback?: React.ReactNode;
  redirectTo?: string;
}

// Loading component
function AuthLoadingScreen() {
  return (
    <div className="min-h-screen bg-gray-50 flex items-center justify-center">
      <div className="text-center">
        <div className="inline-flex items-center justify-center w-16 h-16 bg-blue-600 rounded-2xl mb-6 shadow-lg">
          <Shield className="w-8 h-8 text-white" />
        </div>
        <div className="flex items-center justify-center space-x-2 mb-4">
          <Loader2 className="w-6 h-6 text-blue-600 animate-spin" />
          <span className="text-lg font-medium text-gray-900">Verifying access...</span>
        </div>
        <p className="text-gray-600 max-w-md">
          Please wait while we verify your admin credentials and load the dashboard.
        </p>
      </div>
    </div>
  );
}

// Unauthorized access component
function UnauthorizedScreen() {
  return (
    <div className="min-h-screen bg-gray-50 flex items-center justify-center">
      <div className="text-center max-w-md">
        <div className="inline-flex items-center justify-center w-16 h-16 bg-red-100 rounded-2xl mb-6">
          <Shield className="w-8 h-8 text-red-600" />
        </div>
        <h1 className="text-2xl font-bold text-gray-900 mb-4">Access Denied</h1>
        <p className="text-gray-600 mb-6">
          You don't have permission to access this admin area. Please contact your administrator if you believe this is an error.
        </p>
        <button
          onClick={() => window.location.href = '/login'}
          className="bg-blue-600 hover:bg-blue-700 text-white font-medium py-2 px-4 rounded-lg transition-colors"
        >
          Return to Login
        </button>
      </div>
    </div>
  );
}

// Main AuthGuard component
export function AuthGuard({ 
  children, 
  fallback,
  redirectTo = '/login' 
}: AuthGuardProps) {
  const router = useRouter();
  const { user, loading, requiresAuth } = useRequireAuth();

  // Redirect to login if authentication is required
  useEffect(() => {
    if (requiresAuth) {
      router.push(redirectTo as any);
    }
  }, [requiresAuth, router, redirectTo]);

  // Show loading screen while checking authentication
  if (loading) {
    return fallback || <AuthLoadingScreen />;
  }

  // Redirect is handled by useEffect, but show loading while redirecting
  if (requiresAuth) {
    return fallback || <AuthLoadingScreen />;
  }

  // Check if user has admin privileges
  if (user && !user.isAdmin) {
    return <UnauthorizedScreen />;
  }

  // User is authenticated and has admin privileges
  return <>{children}</>;
}

// Higher-order component for protecting pages
export function withAuthGuard<P extends object>(
  Component: React.ComponentType<P>,
  options?: {
    fallback?: React.ReactNode;
    redirectTo?: string;
  }
) {
  const WrappedComponent = (props: P) => {
    return (
      <AuthGuard 
        fallback={options?.fallback}
        redirectTo={options?.redirectTo}
      >
        <Component {...props} />
      </AuthGuard>
    );
  };

  WrappedComponent.displayName = `withAuthGuard(${Component.displayName || Component.name})`;
  
  return WrappedComponent;
}

// Hook to get current admin user (for use within AuthGuard)
export function useCurrentAdmin() {
  const { user } = useRequireAuth();
  return user;
}

// Export default
export default AuthGuard;
