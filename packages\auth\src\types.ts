/**
 * Shared Authentication Types for Encreasl Monorepo
 * Based on Firebase Auth v10 official documentation
 */

import { User as FirebaseUser, UserCredential } from 'firebase/auth';

// ========================================
// CORE AUTHENTICATION TYPES
// ========================================

export interface AuthError {
  code: string;
  message: string;
}

export interface AuthState {
  user: FirebaseUser | null;
  loading: boolean;
  error: AuthError | null;
}

// ========================================
// USER PROFILE TYPES
// ========================================

export interface BaseUserProfile {
  uid: string;
  email: string | null;
  displayName: string | null;
  photoURL: string | null;
  emailVerified: boolean;
  createdAt: Date;
  lastLoginAt: Date | null;
}

export interface AdminUserProfile extends BaseUserProfile {
  isAdmin: boolean;
  isSuperAdmin: boolean;
  role: AdminRole;
  permissions: AdminPermission[];
  department?: string;
  title?: string;
  mfaEnabled: boolean;
  sessionTimeout: number;
  customClaims?: Record<string, unknown>;
  lastActivity?: Date;
  accessLevel: number;
}

export interface RegularUserProfile extends BaseUserProfile {
  // === PROFILE ===
  firstName?: string;
  lastName?: string;
  bio?: string;
  username?: string;

  // === STATUS ===
  isActive: boolean;
  isVerified: boolean;
  accountType: 'free' | 'premium' | 'enterprise';
  subscriptionStatus?: 'active' | 'cancelled' | 'expired' | 'trial';

  // === PREFERENCES ===
  preferences: {
    theme: 'light' | 'dark' | 'auto';
    language: string;
    timezone: string;
    dateFormat: string;
    timeFormat: '12h' | '24h';
    currency: string;
    notifications: {
      email: boolean;
      push: boolean;
      sms: boolean;
      marketing: boolean;
    };
    privacy: {
      profileVisibility: 'public' | 'friends' | 'private';
      showEmail: boolean;
      showPhone: boolean;
      allowSearchByEmail: boolean;
    };
  };

  // === ANALYTICS ===
  analytics: {
    signupSource?: string;
    totalSessions: number;
    totalTimeSpent: number;
  };
}

// ========================================
// AUTHENTICATION METHODS
// ========================================

export interface EmailPasswordCredentials {
  email: string;
  password: string;
}

export interface SignUpData extends EmailPasswordCredentials {
  displayName?: string;
}

export interface AuthMethods {
  signInWithEmailAndPassword: (credentials: EmailPasswordCredentials) => Promise<UserCredential>;
  signUpWithEmailAndPassword: (data: SignUpData) => Promise<UserCredential>;
  signInWithGoogle: () => Promise<UserCredential>;
  signOut: () => Promise<void>;
  sendPasswordResetEmail: (email: string) => Promise<void>;
  sendEmailVerification: () => Promise<void>;
  updateProfile: (data: { displayName?: string; photoURL?: string }) => Promise<void>;
}

// ========================================
// AUTHENTICATION HOOKS
// ========================================

export interface UseAuthReturn {
  user: FirebaseUser | null;
  loading: boolean;
  error: AuthError | null;
  signIn: (credentials: EmailPasswordCredentials) => Promise<UserCredential>;
  signInAdmin: (credentials: EmailPasswordCredentials) => Promise<UserCredential>;
  signUp: (data: SignUpData) => Promise<UserCredential>;
  signOut: () => Promise<void>;
  resetPassword: (email: string) => Promise<void>;
}

export interface UseAdminAuthReturn extends UseAuthReturn {
  profile: AdminUserProfile | null;
  isAdmin: boolean;
  isSuperAdmin: boolean;
  hasPermission: (permission: string) => boolean;
  sessionValid: boolean;
}

// ========================================
// FIREBASE CONFIG TYPES
// ========================================

export interface FirebaseConfig {
  apiKey: string;
  authDomain: string;
  projectId: string;
  storageBucket: string;
  messagingSenderId: string;
  appId: string;
  vapidKey?: string;
}

// ========================================
// AUTHENTICATION EVENTS
// ========================================

export type AuthEventType = 
  | 'sign-in'
  | 'sign-up' 
  | 'sign-out'
  | 'password-reset'
  | 'email-verification'
  | 'profile-update';

export interface AuthEvent {
  type: AuthEventType;
  user: FirebaseUser | null;
  timestamp: Date;
  metadata?: Record<string, any>;
}

// ========================================
// PERMISSION TYPES
// ========================================

export type AdminPermission =
  | '*'
  | 'users.view'
  | 'users.create'
  | 'users.edit'
  | 'users.delete'
  | 'content.view'
  | 'content.create'
  | 'content.edit'
  | 'content.delete'
  | 'content.publish'
  | 'analytics.view'
  | 'settings.view'
  | 'settings.edit'
  | 'system.admin';

export type AdminRole = 
  | 'super-admin'
  | 'admin'
  | 'content-manager'
  | 'editor'
  | 'viewer';

// ========================================
// VALIDATION SCHEMAS
// ========================================

export interface AuthValidation {
  email: (email: string) => boolean;
  password: (password: string) => { valid: boolean; errors: string[] };
  displayName: (name: string) => boolean;
}

// Re-export Firebase types for convenience
export type { User as FirebaseUser, UserCredential } from 'firebase/auth';
