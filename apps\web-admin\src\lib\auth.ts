import {
  signInWithEmailAndPassword,
  signOut,
  onAuthStateChanged,
  User,
  UserCredential
} from 'firebase/auth';
import { firebaseAuth } from './firebase';

// Types
export interface AdminUser {
  uid: string;
  email: string;
  displayName?: string;
  emailVerified: boolean;
  isAdmin: boolean;
  customClaims?: Record<string, unknown>;
}

export interface LoginCredentials {
  email: string;
  password: string;
}

export interface AuthState {
  user: AdminUser | null;
  loading: boolean;
  error: string | null;
}

// Authentication service class
export class AuthService {
  private static instance: AuthService;
  private authStateListeners: ((user: AdminUser | null) => void)[] = [];

  private constructor() {
    // Set up auth state listener
    onAuthStateChanged(firebaseAuth, async (user) => {
      if (user) {
        try {
          // Get fresh token to check custom claims
          const idTokenResult = await user.getIdTokenResult(true);
          const customClaims = idTokenResult.claims;
          
          // Verify user is an admin
          if (!customClaims.isAdmin) {
            console.warn('User is not an admin, signing out');
            await this.signOut();
            return;
          }

          const adminUser: AdminUser = {
            uid: user.uid,
            email: user.email!,
            displayName: user.displayName || undefined,
            emailVerified: user.emailVerified,
            isAdmin: true,
            customClaims,
          };

          this.notifyAuthStateListeners(adminUser);
        } catch (error) {
          console.error('Error processing auth state change:', error);
          this.notifyAuthStateListeners(null);
        }
      } else {
        this.notifyAuthStateListeners(null);
      }
    });
  }

  public static getInstance(): AuthService {
    if (!AuthService.instance) {
      AuthService.instance = new AuthService();
    }
    return AuthService.instance;
  }

  // Sign in with email and password
  public async signIn(credentials: LoginCredentials): Promise<AdminUser> {
    try {
      const { email, password } = credentials;
      
      // Validate input
      if (!email || !password) {
        throw new Error('Email and password are required');
      }

      // Sign in with Firebase
      const userCredential: UserCredential = await signInWithEmailAndPassword(
        firebaseAuth,
        email,
        password
      );

      const user = userCredential.user;
      
      // Get ID token and verify admin status via API
      const idToken = await user.getIdToken();

      // Call admin verification API
      const response = await fetch('/api/auth/verify-admin', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ idToken }),
      });

      if (!response.ok) {
        await this.signOut();
        const errorData = await response.json();
        throw new Error(errorData.error || 'Admin verification failed');
      }

      const { user: verifiedUser } = await response.json();
      const customClaims = verifiedUser.customClaims;

      // Verify email is verified
      if (!user.emailVerified) {
        await this.signOut();
        throw new Error('Email verification required. Please verify your email address.');
      }

      const adminUser: AdminUser = {
        uid: user.uid,
        email: user.email!,
        displayName: user.displayName || undefined,
        emailVerified: user.emailVerified,
        isAdmin: true,
        customClaims,
      };

      return adminUser;
    } catch (error) {
      console.error('Sign in error:', error);
      
      if (error instanceof Error) {
        // Handle specific Firebase auth errors
        if (error.message.includes('auth/user-not-found')) {
          throw new Error('No account found with this email address.');
        } else if (error.message.includes('auth/wrong-password')) {
          throw new Error('Incorrect password.');
        } else if (error.message.includes('auth/invalid-email')) {
          throw new Error('Invalid email address.');
        } else if (error.message.includes('auth/user-disabled')) {
          throw new Error('This account has been disabled.');
        } else if (error.message.includes('auth/too-many-requests')) {
          throw new Error('Too many failed login attempts. Please try again later.');
        }
        throw error;
      }
      
      throw new Error('An unexpected error occurred during sign in.');
    }
  }

  // Sign out
  public async signOut(): Promise<void> {
    try {
      await signOut(firebaseAuth);
    } catch (error) {
      console.error('Sign out error:', error);
      throw new Error('Failed to sign out');
    }
  }

  // Get current user
  public getCurrentUser(): User | null {
    return firebaseAuth.currentUser;
  }

  // Get ID token for API calls
  public async getIdToken(forceRefresh = false): Promise<string | null> {
    const user = this.getCurrentUser();
    if (!user) return null;
    
    try {
      return await user.getIdToken(forceRefresh);
    } catch (error) {
      console.error('Error getting ID token:', error);
      return null;
    }
  }

  // Check if user is authenticated
  public isAuthenticated(): boolean {
    return this.getCurrentUser() !== null;
  }

  // Add auth state listener
  public onAuthStateChange(callback: (user: AdminUser | null) => void): () => void {
    this.authStateListeners.push(callback);
    
    // Return unsubscribe function
    return () => {
      const index = this.authStateListeners.indexOf(callback);
      if (index > -1) {
        this.authStateListeners.splice(index, 1);
      }
    };
  }

  // Notify all auth state listeners
  private notifyAuthStateListeners(user: AdminUser | null): void {
    this.authStateListeners.forEach(callback => callback(user));
  }
}

// Export singleton instance
export const authService = AuthService.getInstance();

// Convenience functions
export const signInAdmin = (credentials: LoginCredentials) => authService.signIn(credentials);
export const signOutAdmin = () => authService.signOut();
export const getCurrentAdmin = () => authService.getCurrentUser();
export const getAdminIdToken = (forceRefresh?: boolean) => authService.getIdToken(forceRefresh);
export const isAdminAuthenticated = () => authService.isAuthenticated();
